<!-- Generated by SpecStory -->

# 生成接口文档的请求 (2025-08-13 02:32Z)

_**User**_

在ZTE-MES-MANUFACTURESHARE-CENTERFACTORY的根目录下面生成CustomerItemsController类的getCustomerItemsByCustomerAndMode方法的接口文档（注意：只生成这一个接口的），要求包含出入参，完整URL，请求示例，响应结果，异常等信息

---

_**Assistant**_

我需要首先找到CustomerItemsController类和getCustomerItemsByCustomerAndMode方法，然后生成对应的接口文档。让我开始查找这个控制器。

---

<details>
                <summary>Searched codebase "CustomerItemsController getCustomerItemsByCustomerAndMode方法" • **15** results</summary>
            
| File | Lines |
|------|-------|
| `src/main/java/com/zte/application/impl/CustomerItemsServiceImpl.java` | L1187-1237 |
| `src/main/java/com/zte/infrastructure/persistence/mybatis/CustomerItemsRepository.xml` | L331-357 |
| `src/main/java/com/zte/domain/model/CustomerItemsRepository.java` | Lundefined-57 |
| `src/main/java/com/zte/interfaces/CustomerItemsController.java` | L116-145 |
| `src/main/java/com/zte/application/CustomerItemsService.java` | Lundefined-54 |
| `src/main/java/com/zte/application/impl/CustomerItemsServiceImpl.java` | L1150-1198 |
| `src/main/java/com/zte/interfaces/CustomerItemsController.java` | L92-119 |
| `src/test/java/com/zte/application/impl/CustomerItemsServiceImplTest.java` | L2879-2928 |
| `src/main/java/com/zte/application/impl/CustomerItemsServiceImpl.java` | L274-306 |
| `src/test/java/com/zte/application/impl/CustomerItemsServiceImplTest.java` | L3064-3115 |
| `src/main/java/com/zte/application/impl/CustomerItemsServiceImpl.java` | L1255-1303 |
| `src/main/java/com/zte/infrastructure/persistence/mybatis/CustomerItemsRepository.xml` | L353-395 |
| `src/main/java/com/zte/domain/model/CustomerItemsRepository.java` | L53-65 |
| `src/test/java/com/zte/application/impl/CustomerItemsServiceImplTest.java` | L3021-3066 |
| `src/test/java/com/zte/application/impl/CustomerItemsServiceImplTest.java` | L2832-2881 |

</details>

---

现在让我找到DTO类的定义，以便了解完整的接口参数结构：

---

Read file: undefined

---

Read file: undefined

---

<details>
                <summary>Searched codebase "CustomerItemsController RequestMapping 基础路径" • **15** results</summary>
            
| File | Lines |
|------|-------|
| `src/main/java/com/zte/interfaces/CustomerItemsController.java` | L36-74 |
| `src/main/java/com/zte/interfaces/CustomerItemsController.java` | Lundefined-45 |
| `src/main/java/com/zte/interfaces/CustomerItemsController.java` | L71-95 |
| `src/main/java/com/zte/interfaces/CustomerItemsController.java` | L92-119 |
| `src/main/java/com/zte/interfaces/CustomerItemsController.java` | L227-247 |
| `src/main/java/com/zte/interfaces/CustomerItemsController.java` | L199-231 |
| `src/main/java/com/zte/interfaces/CustomerItemsController.java` | L116-145 |
| `src/main/java/com/zte/interfaces/CustomerItemsController.java` | L142-163 |
| `src/main/java/com/zte/interfaces/BsItemInfoController.java` | Lundefined-47 |
| `src/main/java/com/zte/interfaces/BsItemInfoController.java` | L39-104 |
| `src/main/java/com/zte/interfaces/CustomerItemsController.java` | L159-185 |
| `src/main/java/com/zte/interfaces/CfItemExceptionFilingController.java` | L19-51 |
| `src/main/java/com/zte/interfaces/BsPremanuItemInfoController.java` | L42-78 |
| `src/main/java/com/zte/interfaces/CivControlController.java` | L16-49 |
| `src/main/java/com/zte/interfaces/BsItemInfoController.java` | L519-536 |

</details>

---

现在让我查找错误码和消息定义，以及应用配置信息：

---

<details>
                <summary>Searched codebase "CUSTOMER_NAME_NULL COOPERATION_MODE_NULL MAX_ITEM_NO_IS_ONCE 错误消息定义" • **15** results</summary>
            
| File | Lines |
|------|-------|
| `src/main/java/com/zte/common/model/MessageId.java` | L1491-1504 |
| `src/main/resources/messages_en_US.properties` | L35-78 |
| `src/main/java/com/zte/application/impl/CustomerItemsServiceImpl.java` | L1520-1545 |
| `src/test/java/com/zte/application/impl/CustomerItemsServiceImplTest.java` | L2166-2186 |
| `src/main/java/com/zte/common/model/MessageId.java` | L1066-1091 |
| `src/main/java/com/zte/common/utils/Constant.java` | L1152-1189 |
| `src/main/resources/messages_zh_CN.properties` | L27-47 |
| `src/main/resources/messages.properties` | L42-72 |
| `src/test/java/com/zte/application/impl/CustomerItemsServiceImplTest.java` | L2720-2762 |
| `src/main/java/com/zte/common/model/MessageId.java` | L603-648 |
| `src/main/resources/messages_en_US.properties` | Lundefined-37 |
| `src/main/java/com/zte/common/model/MessageId.java` | L1088-1114 |
| `src/main/resources/messages_en_US.properties` | L613-639 |
| `src/main/java/com/zte/common/model/MessageId.java` | L1336-1371 |
| `src/main/java/com/zte/application/impl/CustomerItemsServiceImpl.java` | L1187-1237 |

</details>

---

<details>
                <summary>Searched codebase "server port application properties 配置" • **15** results</summary>
            
| File | Lines |
|------|-------|
| `src/main/resources/application.yml` | Lundefined-54 |
| `src/main/java/com/zte/Application.java` | L29-85 |
| `src/main/resources/application.yml` | L107-151 |
| `src/main/resources/bootstrap.yml` | Lundefined-26 |
| `src/main/resources/application.yml` | L146-171 |
| `src/main/resources/register.properties` | Lundefined-21 |
| `src/main/resources/common.properties` | Lundefined-16 |
| `src/main/java/com/zte/Application.java` | Lundefined-44 |
| `src/main/resources/redis.properties` | Lundefined-23 |
| `src/main/java/com/zte/Application.java` | L80-136 |
| `pom.xml` | L373-405 |
| `src/main/resources/application.yml` | L51-72 |
| `src/main/resources/application.yml` | L70-111 |
| `src/main/java/com/zte/common/ConstantInterface.java` | Lundefined-53 |
| `src/main/java/com/zte/domain/model/UrlConfig.java` | Lundefined-79 |

</details>

---

现在让我读取错误消息的中文配置：

---

Read file: undefined

---

Read file: undefined

---

现在我已经收集了足够的信息，让我创建这个接口的完整文档。基于我找到的代码，我可以看到：

1. 接口位于`CustomerItemsController`类中
2. 方法名为`getCustomerItemsByCustomerAndMode`
3. 使用POST请求
4. 端口为8080，应用名为`zte-mes-manufactureshare-centerfactory`
5. 包含详细的参数验证和错误处理

现在我将生成完整的接口文档：

---



---

我已经成功生成了`getCustomerItemsByCustomerAndMode`方法的完整接口文档。这个文档包含了你要求的所有信息：

## 文档内容概述：

1. **接口基本信息**：
   - 接口名称、请求方式（POST）、Content-Type
   - 完整URL：`http://localhost:8080/zte-mes-manufactureshare-centerfactory/customerItemsCtrl/getCustomerItemsByCustomerAndMode`

2. **入参说明**：
   - `customerName`（必填）：客户名称
   - `cooperationMode`（必填）：合作模式  
   - `itemNoList`（可选）：ZTE代码列表，最多500个

3. **请求示例**：
   - 提供了基本查询和带ZTE代码列表的两种示例
   - 包含CURL命令示例

4. **响应结果**：
   - 详细的成功响应格式和字段说明
   - 完整的客户物料信息字段定义（包含30+个字段）

5. **异常处理**：
   - 业务异常：客户名称为空、合作模式为空、ZTE代码超限
   - 系统异常：参数格式错误、服务器内部错误
   - 详细的错误码说明和解决方案

6. **其他信息**：
   - 注意事项和最佳实践
   - 使用场景说明
   - 版本信息

这个接口文档基于实际的源代码分析生成，包含了真实的参数验证逻辑、错误处理机制和数据结构，可以直接用于开发和API对接。

---


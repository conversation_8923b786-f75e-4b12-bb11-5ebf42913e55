set lock_timeout='5000ms';
ALTER TABLE mes_centerfactory.quality_code_info add if not exists node_sn varchar(64) NOT null default '';
COMMENT ON COLUMN mes_centerfactory.quality_code_info.node_sn IS '虚拟子条码(非多节点时子条吗和条码相同)';

drop index if exists uni_qci_sn_task_no;
CREATE INDEX if not exists idx_qci_sn ON mes_centerfactory.quality_code_info USING btree (sn);
CREATE UNIQUE INDEX if not exists uni_qci_node_sn_task_no ON mes_centerfactory.quality_code_info USING btree (node_sn, task_no);


ALTER TABLE mes_centerfactory.push_model_sn_test_record ADD if not exists node_sn varchar(40) NULL;
COMMENT ON COLUMN mes_centerfactory.push_model_sn_test_record.node_sn IS '设备sn';
CREATE TABLE if not EXISTS push_std_model_sn_data_sub (
	id varchar(64) NOT NULL,
	sn varchar(64) NOT NULL,
	task_no varchar(255) NOT NULL,
	trade_data_log_id varchar(64) NOT NULL,
	priority int4 NULL,
	create_date timestamp(0) DEFAULT sysdate NOT NULL,
	create_by varchar(64) NULL,
	last_updated_date timestamp(0) DEFAULT sysdate NOT NULL,
	last_updated_by varchar(64) NULL,
	enabled_flag varchar(1) DEFAULT 'Y' NULL,
	push_status int4 NULL,
	push_date timestamp(0) NULL,
	push_error_msg varchar NULL,
	push_fail_count int4 DEFAULT 0 NULL,
	curr_process varchar(64) DEFAULT '10' NULL,
	CONSTRAINT push_std_model_sn_data_sub_pk PRIMARY KEY (id)
);
CREATE INDEX if not EXISTS idx_psmsds_sn ON push_std_model_sn_data_sub (sn,task_no,priority);
CREATE INDEX if not EXISTS idx_psmsds_create_date ON push_std_model_sn_data_sub (create_date);
CREATE INDEX if not EXISTS idx_psmsds_trade_data_log_id ON push_std_model_sn_data_sub (trade_data_log_id);
COMMENT ON TABLE push_std_model_sn_data_sub IS '条码推送子表';

-- Column comments

COMMENT ON COLUMN push_std_model_sn_data_sub.trade_data_log_id IS '交易数据推送日志Id';
COMMENT ON COLUMN push_std_model_sn_data_sub.priority IS '优先级';
COMMENT ON COLUMN push_std_model_sn_data_sub.push_status IS '当前进程数据推送状态 0:待推送 1:已推送未回调 2:回调成功 8:数据或推送异常 9:回调结果异常';
COMMENT ON COLUMN push_std_model_sn_data_sub.push_date IS '推送时间';
COMMENT ON COLUMN push_std_model_sn_data_sub.push_error_msg IS '推送失败原因';
COMMENT ON COLUMN push_std_model_sn_data_sub.push_fail_count IS '推送失败次数';
COMMENT ON COLUMN push_std_model_sn_data_sub.curr_process IS '当前推送进程 10初始状态 20:产品SN上报';

ALTER TABLE push_std_model_rcv_bill_detail ADD if not exists rcv_no varchar(256)  NULL;
COMMENT ON COLUMN push_std_model_rcv_bill_detail.rcv_no IS '凭证单号';

create TABLE IF NOT EXISTS hybridcloud_inventory_detail (
    id varchar(64) not null,
    inventory_date timestamp not null, -- 库存日期
    config_model varchar(32) not null, -- 三段码
    mpn varchar(32) not null, -- fixbomID
    inventory_qty int4 not null, -- 库存数量
    delivered_quantity int4 not null, -- 厂家收到PO已发货量
    remark varchar(500) NULL, -- 备注
    create_by varchar(20) not NULL, -- 创建人
    create_date timestamp(0) not NULL DEFAULT LOCALTIMESTAMP, -- 创建日期
    last_updated_by varchar(20) not NULL, -- 修改人
    last_updated_date timestamp(0) NULL DEFAULT LOCALTIMESTAMP, -- 修改日期
    enabled_flag varchar(1) not NULL DEFAULT 'Y'::character varying, -- 是否有效
    CONSTRAINT pk_hybridcloud_inventory_detail PRIMARY KEY (id)
);
create index IF NOT EXISTS idx_hid_inventory_date ON hybridcloud_inventory_detail using btree (inventory_date);

comment on table hybridcloud_inventory_detail is '混合云库存明细表';
comment on column hybridcloud_inventory_detail.inventory_date is '库存日期';
comment on column hybridcloud_inventory_detail.config_model is '三段码';
comment on column hybridcloud_inventory_detail.mpn is 'fixbomID';
comment on column hybridcloud_inventory_detail.inventory_qty is '库存数量';
comment on column hybridcloud_inventory_detail.delivered_quantity is '厂家收到PO已发货量';
comment on column hybridcloud_inventory_detail.remark is '备注';
comment on column hybridcloud_inventory_detail.create_by is '创建人';
comment on column hybridcloud_inventory_detail.create_date is '创建日期';
comment on column hybridcloud_inventory_detail.last_updated_by is '修改人';
comment on column hybridcloud_inventory_detail.last_updated_date is '修改日期';
comment on column hybridcloud_inventory_detail.enabled_flag is '是否有效';


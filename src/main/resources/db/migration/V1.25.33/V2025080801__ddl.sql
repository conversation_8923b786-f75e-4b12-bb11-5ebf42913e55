-- PostgreSQL table creation for Original Factory Assembly Components (原厂组合件)
CREATE TABLE if not exists oem_assembly_component (
    id int8 NOT NULL,
    delivery_no VARCHAR(255) NOT NULL,
    purchase_po_no VARCHAR(255) NOT NULL,
    assembly_product_code VARCHAR(255) NOT NULL,
    assembly_product_name VA<PERSON>HAR(255) NOT NULL,
    assembly_product_sn VARCHAR(255) NOT NULL,
    assembly_supplier_code VARCHAR(255) NOT NULL,
    assembly_supplier_name VARCHAR(255) NOT NULL,
    sub_product_code VARCHAR(100) NOT NULL,
    sub_product_name VARCHAR(255) NOT NULL,
    sub_product_sn VARCHAR(255),
    sub_quantity int4 NOT NULL,
    sub_type VARCHAR(64),
    sub_model VARCHAR(255),
    sub_brand VARCHAR(255),
    sub_env_attribute VARCHAR(64),
    sub_unit VARCHAR(255) NOT NULL,
    upload_time timestamp(0),
    last_modified_by VARCHAR(100),
    create_by varchar(20) NOT NULL DEFAULT ''::character varying,
	create_date timestamp(0) NOT NULL DEFAULT sysdate,
	last_updated_by varchar(20) NOT NULL DEFAULT ''::character varying,
	last_updated_date timestamp(0) NOT NULL DEFAULT sysdate,
	enabled_flag varchar(1) NOT NULL DEFAULT 'Y'::character varying,
	CONSTRAINT pk_oem_assembly_component PRIMARY KEY (id)
);

-- Create indexes for better performance
CREATE INDEX if not exists idx_oem_assembly_delivery_no ON oem_assembly_component(delivery_no);
CREATE INDEX if not exists idx_oem_assembly_po_no ON oem_assembly_component(purchase_po_no);
CREATE INDEX if not exists idx_oem_assembly_product_code ON oem_assembly_component(assembly_product_code);
CREATE UNIQUE INDEX if not exists uni_oem_assembly_product_sn_sub_sn ON oem_assembly_component(assembly_product_sn,sub_product_sn);
CREATE INDEX if not exists idx_oem_sub_product_code ON oem_assembly_component(sub_product_code);
CREATE INDEX if not exists idx_oem_sub_product_sn ON oem_assembly_component(sub_product_sn);
CREATE INDEX if not exists idx_oem_last_updated_date ON oem_assembly_component(last_updated_date);

-- Add table comment
COMMENT ON TABLE oem_assembly_component IS '原厂组合件信息';

COMMENT ON COLUMN oem_assembly_component.id IS '主键id(雪花id)';
COMMENT ON COLUMN oem_assembly_component.delivery_no IS '送货单号';
COMMENT ON COLUMN oem_assembly_component.purchase_po_no IS '采购PO号';
COMMENT ON COLUMN oem_assembly_component.assembly_product_code IS '组合件产品代码';
COMMENT ON COLUMN oem_assembly_component.assembly_product_name IS '组合件产品名称';
COMMENT ON COLUMN oem_assembly_component.assembly_product_sn IS '组合件产品SN';
COMMENT ON COLUMN oem_assembly_component.assembly_supplier_code IS '组合件供应商编码';
COMMENT ON COLUMN oem_assembly_component.assembly_supplier_name IS '组合件供应商名称';
COMMENT ON COLUMN oem_assembly_component.sub_product_code IS '子部件产品代码';
COMMENT ON COLUMN oem_assembly_component.sub_product_name IS '子部件产品名称';
COMMENT ON COLUMN oem_assembly_component.sub_product_sn IS '子部件产品SN';
COMMENT ON COLUMN oem_assembly_component.sub_quantity IS '子部件数量';
COMMENT ON COLUMN oem_assembly_component.sub_type IS '子部件类型';
COMMENT ON COLUMN oem_assembly_component.sub_model IS '子部件型号';
COMMENT ON COLUMN oem_assembly_component.sub_brand IS '子部件品牌';
COMMENT ON COLUMN oem_assembly_component.sub_env_attribute IS '子部件环保属性';
COMMENT ON COLUMN oem_assembly_component.sub_unit IS '子部件单位';
COMMENT ON COLUMN oem_assembly_component.upload_time IS '上传时间';
COMMENT ON COLUMN oem_assembly_component.last_modified_by IS '最后修改人';

-- 记录箱内容发生过变化的箱号
CREATE TABLE if not exists push_std_model_changed_carton (
    id varchar(64) NOT NULL, -- 主键id
    carton_no varchar(256), -- 箱包号
    push_status int4 NOT NULL DEFAULT 0, -- 数据推送状态 0:待推送 1:已推送未回调 2:回调成功 8:数据或推送异常 9:回调结果异常
    push_date timestamp NULL, -- 数据推送时间
    error_msg varchar(2000) NULL, -- 异常信息
    push_fail_count int4 NOT NULL DEFAULT 0, -- 推送失败次数
    create_by varchar(32) NOT NULL DEFAULT ''::character varying,
    create_date timestamp NOT NULL DEFAULT sysdate,
    last_updated_by varchar(32) NOT NULL DEFAULT ''::character varying,
    last_updated_date timestamp NOT NULL DEFAULT sysdate,
    enabled_flag varchar(1) NOT NULL DEFAULT 'Y'::character varying,
    CONSTRAINT pk_push_std_model_changed_carton PRIMARY KEY (id)
);
CREATE INDEX if not exists idx_psmcc_last_updated_date ON push_std_model_changed_carton (last_updated_date);
CREATE INDEX if not exists idx_psmcc_carton_no ON push_std_model_changed_carton (carton_no);

COMMENT ON TABLE push_std_model_changed_carton IS '记录箱内容发生过变化的箱号';
-- Column comments
COMMENT ON COLUMN push_std_model_changed_carton.carton_no IS '箱包号';
COMMENT ON COLUMN push_std_model_changed_carton.push_status IS '数据推送状态 0:待推送 1:已推送未回调 2:回调成功 3:无需推送 8:数据或推送异常 9:回调结果异常';
COMMENT ON COLUMN push_std_model_changed_carton.push_date IS '数据推送时间';
COMMENT ON COLUMN push_std_model_changed_carton.error_msg IS '异常信息';
COMMENT ON COLUMN push_std_model_changed_carton.push_fail_count IS '推送失败次数';


COMMENT ON COLUMN mes_centerfactory.push_std_model_data.curr_process IS '当前推送进程 10:排产信息推送 20:工单发放 50:工单关闭';

ALTER TABLE push_std_model_sn_data ADD if not exists item_no varchar(20) NOT NULL;
COMMENT ON COLUMN push_std_model_sn_data.item_no IS '物料代码';


CREATE TABLE if not exists push_model_sn_test_record (
	request_id varchar(64) NOT NULL, -- 主键
	station_id varchar(64) NOT NULL, -- 中试测试记录id
	"type" varchar(64) NULL, -- 设备类型
	directive_number varchar(64) NOT NULL, -- 生产指令
	workorder_id varchar(240) NOT NULL, -- 中兴任务号
	sn varchar(32) NOT NULL, -- 整机条码
	brand varchar(10) NULL, -- 制造品牌名
	board_sn varchar(2000) NULL, -- 主板条码，存在多个时，使用,分隔
	model varchar(200) NULL, -- 任务附加属性表-三段机型
	station_name varchar(60) NULL, -- 站位名称
	started_time varchar(50) NULL, -- 检测的开始时间
	finished_time varchar(50) NULL, -- 检测的结束时间
	"result" varchar(10) NULL, -- 检测结果,取值范围：Pass/Fail
	message varchar(2000) NULL, -- 工序站位检查结果失败时的原因说明
	oss_file_key varchar(240) NOT NULL, -- oss测试文件
	oss_file_url varchar(240) NULL, -- oss测试文件url
	manufacturer_name varchar(20) NULL, -- 工厂名称
	action_code varchar(50) NULL, -- 维修类型
	action_msg varchar(2000) NULL, -- 维修详情
	finish_rework_time varchar(50) NULL, -- 维修时间
	rework_time varchar(50) NULL, -- 送修时间
	upload_status varchar(2) NULL DEFAULT '0'::character varying, -- 状态 0 待推送 1 整机测试数据推送成功  2 整机测试数据推送失败
	upload_msg varchar(2000) NULL, -- 整机测试数据推送结果
	station_upload_status varchar(2) NULL DEFAULT '0'::character varying, -- 状态 0 待推送 1 站位分析结果推送成功  2 站位分析结果推送失败
	station_upload_msg varchar(2000) NULL, -- 站位分析结果推送结果
	file_upload_status varchar(2) NULL DEFAULT '0'::character varying, -- 状态 0 待推送 1 oss日志文件推送成功  2 oss日志文件推送失败
	file_upload_msg varchar(2000) NULL, --  oss日志文件推送结果
	enabled_flag varchar(1) NOT NULL DEFAULT 'Y'::character varying,
	create_date timestamp NOT NULL DEFAULT statement_timestamp()::date,
	last_updated_date timestamp NOT NULL DEFAULT statement_timestamp()::date,
	create_by varchar(32) NOT NULL,
	last_updated_by varchar(32) NOT NULL,
	factory_id int4 NOT NULL, -- 工厂id
	CONSTRAINT push_test_data_pk PRIMARY KEY (request_id)
);
CREATE INDEX if not exists idx_pmstr_station_id ON push_model_sn_test_record USING btree (station_id);
CREATE INDEX if not exists idx_pmstr_sn_idx ON push_model_sn_test_record USING btree (sn);

COMMENT ON TABLE push_model_sn_test_record IS '标模任务条码推送测试记录表';

-- Column comments

COMMENT ON COLUMN push_model_sn_test_record.request_id IS '主键';
COMMENT ON COLUMN push_model_sn_test_record.station_id IS '中试测试记录id';
COMMENT ON COLUMN push_model_sn_test_record."type" IS '设备类型';
COMMENT ON COLUMN push_model_sn_test_record.directive_number IS '生产指令';
COMMENT ON COLUMN push_model_sn_test_record.workorder_id IS '中兴任务号';
COMMENT ON COLUMN push_model_sn_test_record.sn IS '整机条码';
COMMENT ON COLUMN push_model_sn_test_record.brand IS '制造品牌名';
COMMENT ON COLUMN push_model_sn_test_record.board_sn IS '主板条码，存在多个时，使用,分隔';
COMMENT ON COLUMN push_model_sn_test_record.model IS '任务附加属性表-三段机型';
COMMENT ON COLUMN push_model_sn_test_record.station_name IS '站位名称';
COMMENT ON COLUMN push_model_sn_test_record.started_time IS '检测的开始时间';
COMMENT ON COLUMN push_model_sn_test_record.finished_time IS '检测的结束时间';
COMMENT ON COLUMN push_model_sn_test_record."result" IS '检测结果,取值范围：Pass/Fail';
COMMENT ON COLUMN push_model_sn_test_record.message IS '工序站位检查结果失败时的原因说明';
COMMENT ON COLUMN push_model_sn_test_record.oss_file_key IS 'oss测试文件';
COMMENT ON COLUMN push_model_sn_test_record.oss_file_url IS 'oss测试文件url';
COMMENT ON COLUMN push_model_sn_test_record.manufacturer_name IS '工厂名称';
COMMENT ON COLUMN push_model_sn_test_record.action_code IS '维修类型';
COMMENT ON COLUMN push_model_sn_test_record.action_msg IS '维修详情';
COMMENT ON COLUMN push_model_sn_test_record.finish_rework_time IS '维修时间';
COMMENT ON COLUMN push_model_sn_test_record.rework_time IS '送修时间';
COMMENT ON COLUMN push_model_sn_test_record.upload_status IS '状态 0 待推送 1 整机测试数据推送成功  2 整机测试数据推送失败';
COMMENT ON COLUMN push_model_sn_test_record.upload_msg IS '整机测试数据推送结果';
COMMENT ON COLUMN push_model_sn_test_record.station_upload_status IS '状态 0 待推送 1 站位分析结果推送成功  2 站位分析结果推送失败';
COMMENT ON COLUMN push_model_sn_test_record.station_upload_msg IS '站位分析结果推送结果';
COMMENT ON COLUMN push_model_sn_test_record.file_upload_status IS '状态 0 待推送 1 oss日志文件推送成功  2 oss日志文件推送失败';
COMMENT ON COLUMN push_model_sn_test_record.file_upload_msg IS ' oss日志文件推送结果';
COMMENT ON COLUMN push_model_sn_test_record.factory_id IS '工厂id';

-- Permissions



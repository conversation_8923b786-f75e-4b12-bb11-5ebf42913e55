-- 标模领料信息推送表
CREATE TABLE if not exists push_std_model_rcv_bill (
    bill_no varchar(256) NOT NULL, -- 领料单号
    task_no varchar(256) NOT NULL, -- 任务号
    customer_name varchar(64), -- 客户名称或客户编码
    push_status int4 NOT NULL DEFAULT 0,
    push_date timestamp, -- 推送时间
    error_msg varchar(2000),
    push_fail_count int4 NOT NULL DEFAULT 0,
	create_by varchar(32) NOT NULL DEFAULT '',
	create_date timestamp NOT NULL DEFAULT sysdate,
	last_updated_by varchar(32) NOT NULL DEFAULT '',
	last_updated_date timestamp NOT NULL DEFAULT sysdate,
	enabled_flag varchar(1) NOT NULL DEFAULT 'Y',
	CONSTRAINT pk_push_std_model_rcv_bill PRIMARY KEY (bill_no)
);
CREATE INDEX if not exists idx_psmrb_last_updated_date ON push_std_model_rcv_bill (last_updated_date);
CREATE INDEX if not exists idx_psmrb_task_no ON push_std_model_rcv_bill (task_no);
COMMENT ON TABLE push_std_model_rcv_bill IS '标模领料信息推送表';
-- Column comments
COMMENT ON COLUMN push_std_model_rcv_bill.bill_no IS '领料单号';
COMMENT ON COLUMN push_std_model_rcv_bill.task_no IS '任务号';
COMMENT ON COLUMN push_std_model_rcv_bill.customer_name IS '客户名称或客户编码';
COMMENT ON COLUMN push_std_model_rcv_bill.push_status IS '推送状态 0:待推送 1:已推送未回调 2:回调成功 8:数据或推送异常 9:回调结果异常';
COMMENT ON COLUMN push_std_model_rcv_bill.push_date IS '当前进程数据推送时间';
COMMENT ON COLUMN push_std_model_rcv_bill.error_msg IS '异常信息';

COMMENT ON COLUMN mes_centerfactory.push_std_model_sn_data.curr_process IS '当前推送进程 25:整机测试数据上报 30:产品SN上报 40:成品入库上报';

-- 修改默认值为sysdate
ALTER TABLE mes_centerfactory.push_board_data_head ALTER COLUMN create_date SET DEFAULT sysdate;
ALTER TABLE mes_centerfactory.push_board_data_head ALTER COLUMN last_updated_date SET DEFAULT sysdate;
ALTER TABLE mes_centerfactory.push_board_data_detail ALTER COLUMN create_date SET DEFAULT sysdate;
ALTER TABLE mes_centerfactory.push_board_data_detail ALTER COLUMN last_updated_date SET DEFAULT sysdate;
ALTER TABLE mes_centerfactory.push_board_data_process ALTER COLUMN create_date SET DEFAULT sysdate;
ALTER TABLE mes_centerfactory.push_board_data_process ALTER COLUMN last_updated_date SET DEFAULT sysdate;
ALTER TABLE mes_centerfactory.push_std_model_data ALTER COLUMN create_date SET DEFAULT sysdate;
ALTER TABLE mes_centerfactory.push_std_model_data ALTER COLUMN last_updated_date SET DEFAULT sysdate;
ALTER TABLE mes_centerfactory.push_std_model_sn_data ALTER COLUMN create_date SET DEFAULT sysdate;
ALTER TABLE mes_centerfactory.push_std_model_sn_data ALTER COLUMN last_updated_date SET DEFAULT sysdate;
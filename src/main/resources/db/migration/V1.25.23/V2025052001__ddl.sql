-- 标模领料信息推送明细表
CREATE TABLE if not exists push_std_model_rcv_bill_detail (
    id varchar(64) NOT NULL, -- 主键id
    order_no varchar(256) NOT NULL, -- 单据号
    material_mpn varchar(256) NOT NULL, -- 客户物料代码
    storage_area int2 NOT NULL DEFAULT 1, -- 库位(1原箱区2混箱区)，默认1
    carton_no varchar(256), -- 箱包号
    sn_no varchar(256) NOT NULL, -- 发料条码
	create_by varchar(32) NOT NULL DEFAULT '',
	create_date timestamp NOT NULL DEFAULT sysdate,
	last_updated_by varchar(32) NOT NULL DEFAULT '',
	last_updated_date timestamp NOT NULL DEFAULT sysdate,
	enabled_flag varchar(1) NOT NULL DEFAULT 'Y',
	CONSTRAINT pk_push_std_model_rcv_bill_detail PRIMARY KEY (id)
);
CREATE INDEX if not exists idx_psmrbd_last_updated_date ON push_std_model_rcv_bill_detail (last_updated_date);
CREATE INDEX if not exists idx_psmrbd_order_no ON push_std_model_rcv_bill_detail (order_no);
CREATE INDEX if not exists idx_psmrbd_carton_no ON push_std_model_rcv_bill_detail (carton_no, sn_no, enabled_flag);
CREATE INDEX if not exists idx_psmrbd_carton_no ON push_std_model_rcv_bill_detail (sn_no);
COMMENT ON TABLE push_std_model_rcv_bill_detail IS '标模领料信息推送明细表';
-- Column comments
COMMENT ON COLUMN push_std_model_rcv_bill_detail.order_no IS '单据号';
COMMENT ON COLUMN push_std_model_rcv_bill_detail.material_mpn IS '客户物料代码';
COMMENT ON COLUMN push_std_model_rcv_bill_detail.storage_area IS '库位(1原箱区2混箱区)，默认1';
COMMENT ON COLUMN push_std_model_rcv_bill_detail.carton_no IS '箱包号';
COMMENT ON COLUMN push_std_model_rcv_bill_detail.sn_no IS '发料条码';

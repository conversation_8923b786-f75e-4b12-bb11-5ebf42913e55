CREATE TABLE if not exists fix_bom_head (
	id varchar(64) NOT NULL,
	fix_bom_id varchar(64) NOT NULL,
	enabled_flag bpchar(1) DEFAULT 'Y'::bpchar NOT NULL,
	created_by varchar(45) NULL,
	creation_date timestamp DEFAULT statement_timestamp()::date NOT NULL,
	last_updated_by varchar(45) NULL,
	last_update_date timestamp DEFAULT statement_timestamp()::date NOT NULL,
	mbom_version varchar(255) NULL,
	cust_name varchar(100) NULL,
	cust_no varchar(100) NULL,
	CONSTRAINT fixbom_head_pk PRIMARY KEY (id)
);
CREATE UNIQUE INDEX if not exists idx_fbh_fixbom_id ON fix_bom_head USING btree (fix_bom_id);
CREATE INDEX if not exists idx_fbh_last_update_date ON fix_bom_head USING btree (last_update_date);
CREATE INDEX if not exists idx_fbh_cust_no ON fix_bom_head USING btree (cust_no);
COMMENT on TABLE fix_bom_head IS 'fixbom头表';
COMMENT ON COLUMN fix_bom_head.mbom_version IS 'mbom版本';
COMMENT ON COLUMN fix_bom_head.cust_name IS '客户名称';
COMMENT ON COLUMN fix_bom_head.cust_no IS '客户编码';

CREATE TABLE if not exists fix_bom_detail (
	id varchar(64) NOT NULL,
	fix_bom_id varchar(64) NOT NULL,
	item_level varchar(255) NULL,
	item_seq varchar(255) NULL,
	item_type varchar(255) NULL,
	zte_code varchar(64) NULL,
	customer_component_type varchar(64) NULL,
	item_no varchar(255) NULL,
	item_supplier_no varchar(300) NULL,
	item_material_type varchar(255) NULL,
	ITEM_NAME varchar(500) NULL,
	item_number varchar(32) NULL,
	item_group varchar(255) NULL,
	delete_flag varchar(1) NULL,
	require_material_upload varchar(1) NULL,
	is_priced_material varchar(1) NULL,
	upload_by_sn varchar(1) NULL,
	fix_bom_required varchar(1) NULL,
	box_bom_required varchar(1) NULL,
	box_priority int4 NULL,
	enabled_flag varchar(1) DEFAULT 'Y'::character varying NULL,
	zte_code_name varchar(255) NULL,
	created_by varchar(32) NULL,
	creation_date timestamp DEFAULT statement_timestamp()::date NULL,
	last_update_date timestamp NULL,
	last_updated_by varchar(32) NULL,
	item_version varchar(32) NULL,
	CONSTRAINT fixbom_detail_pk PRIMARY KEY (id)
);
CREATE INDEX if not exists idx_fbd_fixbom_id ON fix_bom_detail USING btree (fix_bom_id);
CREATE INDEX if not exists idx_fbd_item_supplier_no ON fix_bom_detail USING btree (item_supplier_no);
CREATE INDEX if not exists idx_fbd_last_update_date ON fix_bom_detail USING btree (last_update_date);
CREATE INDEX if not exists idx_fbd_zte_code ON fix_bom_detail USING btree (zte_code);

COMMENT on TABLE fix_bom_detail IS 'fixbom明细表';
COMMENT ON COLUMN fix_bom_detail.item_level IS '层级';
COMMENT ON COLUMN fix_bom_detail.item_seq IS '序号';
COMMENT ON COLUMN fix_bom_detail.item_type IS '物料类型';
COMMENT ON COLUMN fix_bom_detail.zte_code IS 'ZTE代码';
COMMENT ON COLUMN fix_bom_detail.customer_component_type IS '客户部件类型';
COMMENT ON COLUMN fix_bom_detail.item_no IS '编号';
COMMENT ON COLUMN fix_bom_detail.item_supplier_no IS '供应商料号';
COMMENT ON COLUMN fix_bom_detail.item_material_type IS '物料分类';
COMMENT ON COLUMN fix_bom_detail.ITEM_NAME IS '客户物料名称';
COMMENT ON COLUMN fix_bom_detail.item_number IS '用量';
COMMENT ON COLUMN fix_bom_detail.item_group IS '分组';
COMMENT ON COLUMN fix_bom_detail.delete_flag IS '删除标识';
COMMENT ON COLUMN fix_bom_detail.require_material_upload IS '箱单必须上传物料';
COMMENT ON COLUMN fix_bom_detail.is_priced_material IS '是否计价物件';
COMMENT ON COLUMN fix_bom_detail.upload_by_sn IS '箱单按SN上传';
COMMENT ON COLUMN fix_bom_detail.fix_bom_required IS 'Fixbom需要';
COMMENT ON COLUMN fix_bom_detail.box_bom_required IS '箱单BOM需要';
COMMENT ON COLUMN fix_bom_detail.box_priority IS '箱单优先级';
COMMENT ON COLUMN fix_bom_detail.zte_code_name IS 'ZTE代码名称';
COMMENT ON COLUMN fix_bom_detail.item_version IS '物料版本';

CREATE TABLE if not exists ps_task_extended (
	id varchar(64) NOT NULL,
	bill_no varchar(64) NULL,
	self_supply_prepare_date timestamp NULL,
	full_prepare_date timestamp NULL,
	business_scene varchar(32) NULL,
	bill_bom varchar(64) NULL,
	factory_code varchar(32) NULL,
	customer_no varchar(32) NULL,
	rel_no varchar(64) NULL,
	entity_class varchar(32) NULL,
	task_type varchar(2) NULL,
	customer_part_type varchar(64) NULL,
	customer_item_name varchar(200) NULL,
	task_no varchar(240) NOT NULL,
	fix_bom_id varchar(64) NULL,
	enabled_flag varchar(1) DEFAULT 'Y'::character varying NOT NULL,
	create_date timestamp DEFAULT statement_timestamp()::date NOT NULL,
	last_updated_date timestamp DEFAULT statement_timestamp()::date NOT NULL,
	create_by varchar(32) NOT NULL,
	last_updated_by varchar(32) NOT NULL,
	fix_bom_complete varchar(1) DEFAULT 'N'::character varying NULL,
	expected_completed_date timestamp NULL,
	cbom_error_message varchar(2000) NULL,
	prepare_product_date timestamp NULL,
	prepare_completed_date timestamp NULL,
	CONSTRAINT ps_task_extended_pk PRIMARY KEY (id)
);
CREATE INDEX if not exists idx_ps_task_extended_fix_bom_id ON ps_task_extended USING btree (fix_bom_id);
CREATE INDEX if not exists idx_ps_task_extended_last_updated_date ON ps_task_extended USING btree(last_updated_date);
CREATE UNIQUE index if not exists idx_ps_task_extended_task_no ON ps_task_extended USING btree(task_no);
CREATE INDEX if not exists idx_pse_customer_no ON ps_task_extended USING btree (customer_no);

COMMENT on TABLE ps_task_extended IS '任务扩展属性表';
COMMENT ON COLUMN ps_task_extended.bill_no IS '指令编号';
COMMENT ON COLUMN ps_task_extended.self_supply_prepare_date IS '厂商自供料预计齐套日期';
COMMENT ON COLUMN ps_task_extended.full_prepare_date IS '全部物料预计齐套日期';
COMMENT ON COLUMN ps_task_extended.business_scene IS '业务场景';
COMMENT ON COLUMN ps_task_extended.bill_bom IS '指令BOM';
COMMENT ON COLUMN ps_task_extended.factory_code IS '⼯⼚编码';
COMMENT ON COLUMN ps_task_extended.customer_no IS '客户编码';
COMMENT ON COLUMN ps_task_extended.rel_no IS '关联工单号，基于buffer工单下的改配工单，需要提供此字段';
COMMENT ON COLUMN ps_task_extended.entity_class IS '任务分类';
COMMENT ON COLUMN ps_task_extended.task_type IS '任务类型：根据business_sence转换，若business_sence=buffer，则为2，否则为1；1-正式、2-buffer';
COMMENT ON COLUMN ps_task_extended.customer_part_type IS '客户部件类型：需要新增此字段，在排产回写时保存此字段';
COMMENT ON COLUMN ps_task_extended.customer_item_name IS '客户物料名称（三段码），新增此字段排产回写时保存此字段';
COMMENT ON COLUMN ps_task_extended.task_no IS '任务号';
COMMENT ON COLUMN ps_task_extended.fix_bom_id IS 'fix_bom_id';
COMMENT ON COLUMN ps_task_extended.fix_bom_complete IS 'FIX_BOM_ID 是否处理完成Y完成';
COMMENT ON COLUMN ps_task_extended.expected_completed_date IS '期望完工时间';
COMMENT ON COLUMN ps_task_extended.prepare_product_date IS '预计投产日期';
COMMENT ON COLUMN ps_task_extended.prepare_completed_date IS '预计完工日期';
COMMENT ON COLUMN ps_task_extended.cbom_error_message IS 'cbom生成异常信息';


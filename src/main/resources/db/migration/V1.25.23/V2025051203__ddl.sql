-- 质量码信息表
CREATE TABLE if not exists quality_code_info (
    id varchar(64) NOT NULL, -- 主键id
    sn varchar(64) NOT NULL, -- 条码
    task_no varchar(240) NOT NULL, -- 任务号
    quality_code varchar(200) NOT NULL, -- 质量码
    customer_no varchar(32) NOT NULL, -- 客户编码
    create_by varchar(32) NOT NULL DEFAULT ''::character varying, -- 创建人
    create_date timestamp NOT NULL DEFAULT LOCALTIMESTAMP, -- 创建时间
    last_updated_by varchar(32) NOT NULL DEFAULT ''::character varying, -- 最后更新人
    last_updated_date timestamp NOT NULL DEFAULT LOCALTIMESTAMP, -- 最后更新时间
    enabled_flag varchar(1) NOT NULL DEFAULT 'Y'::character varying, -- 是否有效
    CONSTRAINT pk_quality_code_info PRIMARY KEY (id)
);

CREATE INDEX if not exists idx_qci_last_updated_date ON quality_code_info (last_updated_date);
CREATE UNIQUE INDEX if not exists uni_qci_sn_task_no ON quality_code_info (sn, task_no);
CREATE INDEX if not exists idx_qci_task_no ON quality_code_info (task_no);


-- Column comments
COMMENT ON COLUMN quality_code_info.id IS '主键id';
COMMENT ON COLUMN quality_code_info.sn IS '条码';
COMMENT ON COLUMN quality_code_info.task_no IS '任务号';
COMMENT ON COLUMN quality_code_info.quality_code IS '质量码';
COMMENT ON COLUMN quality_code_info.customer_no IS '客户编码';
COMMENT ON COLUMN quality_code_info.create_by IS '创建人';
COMMENT ON COLUMN quality_code_info.create_date IS '创建时间';
COMMENT ON COLUMN quality_code_info.last_updated_by IS '最后更新人';
COMMENT ON COLUMN quality_code_info.last_updated_date IS '最后更新时间';
COMMENT ON COLUMN quality_code_info.enabled_flag IS '是否有效';

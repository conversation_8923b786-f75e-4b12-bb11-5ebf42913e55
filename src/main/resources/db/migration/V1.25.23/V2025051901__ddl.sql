set lock_timeout='5000ms';
ALTER TABLE push_board_data_process ADD if NOT EXISTS push_number varchar DEFAULT '1' NOT NULL;
COMMENT ON COLUMN push_board_data_process.push_number IS '单条码推送条数';


CREATE TABLE if not exists push_std_model_confirmation (
	task_no varchar(256) NOT NULL, -- 任务号
	item_no varchar(64) NOT NULL, -- 料单代码
	factory_id int4 NOT NULL, -- 工厂id
	confirmation_type int4 NOT NULL DEFAULT 1, -- 任务转正类型 1:无条码入buffer库直接转正 2:入buffer库条码全部入正式库后转正
	push_status int4 NOT NULL DEFAULT 0, -- 数据推送状态 0:待推送 1:已推送未回调 2:回调成功 8:数据或推送异常 9:回调结果异常
	push_date timestamp NULL, -- 数据推送时间
	error_msg varchar(2000) NULL, -- 异常信息
	push_fail_count int4 NOT NULL DEFAULT 0, -- 推送失败次数
	create_by varchar(32) NOT NULL DEFAULT ''::character varying,
	create_date timestamp NOT NULL DEFAULT sysdate,
	last_updated_by varchar(32) NOT NULL DEFAULT ''::character varying,
	last_updated_date timestamp NOT NULL DEFAULT sysdate,
	enabled_flag varchar(1) NOT NULL DEFAULT 'Y'::character varying,
	CONSTRAINT pk_push_std_model_confirmation PRIMARY KEY (task_no)
);
CREATE INDEX if not exists idx_psmc_last_updated_date ON push_std_model_confirmation USING btree (last_updated_date);
COMMENT ON TABLE push_std_model_confirmation IS '已转正标模任务推送记录表';

-- Column comments

COMMENT ON COLUMN push_std_model_confirmation.task_no IS '任务号';
COMMENT ON COLUMN push_std_model_confirmation.item_no IS '料单代码';
COMMENT ON COLUMN push_std_model_confirmation.factory_id IS '工厂id';
COMMENT ON COLUMN push_std_model_confirmation.confirmation_type IS '任务转正类型 1:无条码入buffer库直接转正 2:入buffer库条码全部入正式库后转正';
COMMENT ON COLUMN push_std_model_confirmation.push_status IS '数据推送状态 0:待推送 1:已推送未回调 2:回调成功 8:数据或推送异常 9:回调结果异常';
COMMENT ON COLUMN push_std_model_confirmation.push_date IS '数据推送时间';
COMMENT ON COLUMN push_std_model_confirmation.error_msg IS '异常信息';
COMMENT ON COLUMN push_std_model_confirmation.push_fail_count IS '推送失败次数';


CREATE TABLE if not exists ps_delivery_feedback (
	id varchar(64) NOT NULL, -- 主键ID
	order_type varchar(4) NOT NULL, -- 单据类型(1指令、2⼯单)
	business_scene varchar(64) NOT NULL, -- 工单类型
	order_no varchar(240) NOT NULL, -- 单据编号(指令编号/⼯单编号)
	category varchar(64) NOT NULL, -- 业务分类
	quantity numeric NOT NULL, -- 反馈数量
	date_estimated_completion timestamp NULL, -- 预计完⼯⽇期
	date_expected_completion timestamp NULL, -- 期望完⼯⽇期
	date_material_estimated_prepared timestamp NULL, -- ⼚商⾃供料预计⻬套⽇期
	date_all_material_estimated_prepared timestamp NULL, -- 全部物料预计⻬套⽇期
	time_material_prepared timestamp NULL, -- ⼚商⾃供料实际⻬套⽇期
	date_all_material_prepared timestamp NULL, -- 全部物料实际⻬套⽇期
	date_scheduled_production timestamp NULL, -- 预计投产⽇期
	time_production timestamp NULL, -- 实际投产⽇期
	liability varchar(1) NULL, -- 责任（1阿⾥责任，2⼚商责任）
	abnormal_category_first varchar(64) NULL, -- ⼀级原因分类
	abnormal_category_second varchar(64) NULL, -- ⼆级原因分类
	abnormal_category_third varchar(64) NULL, -- 三级原因分类
	remark varchar(64) NULL, -- 原因
	abnormal_no varchar(64) NULL, -- 延期编号(不能重复)
	operate_type varchar(1) NOT NULL, -- 操作类型，1-人工，2-定时任务
	push_status varchar(1) NOT NULL, -- 推送状态：1-已推送消息未收到阿里回调通知，2-已推送消息已收到阿里回调通知
	push_error_msg varchar(2000) NULL, -- 推送报错消息
	enabled_flag varchar(1) NOT NULL DEFAULT 'Y'::character varying, -- 有效标识，Y-有效，N-无效
	schedule_flag varchar(1) NOT NULL DEFAULT 'N'::character varying, --  定时任务调度标识，Y-已调度，N-未调度
	notes varchar(3000) NULL, -- 备注
	create_by varchar(32) NOT NULL DEFAULT ''::character varying, -- 创建人
	create_date timestamp NOT NULL DEFAULT sysdate, -- 创建时间
	last_updated_by varchar(32) NOT NULL DEFAULT ''::character varying, -- 最后更新人
	last_updated_date timestamp NOT NULL DEFAULT sysdate, -- 最后更新时间
	CONSTRAINT pk_ps_delivery_feedback PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN ps_delivery_feedback.id IS '主键ID';
COMMENT ON COLUMN ps_delivery_feedback.order_type IS '单据类型(1指令、2⼯单)';
COMMENT ON COLUMN ps_delivery_feedback.business_scene IS '工单类型';
COMMENT ON COLUMN ps_delivery_feedback.order_no IS '单据编号(指令编号/⼯单编号)';
COMMENT ON COLUMN ps_delivery_feedback.category IS '业务分类';
COMMENT ON COLUMN ps_delivery_feedback.quantity IS '反馈数量';
COMMENT ON COLUMN ps_delivery_feedback.date_estimated_completion IS '预计完⼯⽇期';
COMMENT ON COLUMN ps_delivery_feedback.date_expected_completion IS '期望完⼯⽇期';
COMMENT ON COLUMN ps_delivery_feedback.date_material_estimated_prepared IS '⼚商⾃供料预计⻬套⽇期';
COMMENT ON COLUMN ps_delivery_feedback.date_all_material_estimated_prepared IS '全部物料预计⻬套⽇期';
COMMENT ON COLUMN ps_delivery_feedback.time_material_prepared IS '⼚商⾃供料实际⻬套⽇期';
COMMENT ON COLUMN ps_delivery_feedback.date_all_material_prepared IS '全部物料实际⻬套⽇期';
COMMENT ON COLUMN ps_delivery_feedback.date_scheduled_production IS '预计投产⽇期';
COMMENT ON COLUMN ps_delivery_feedback.time_production IS '实际投产⽇期';
COMMENT ON COLUMN ps_delivery_feedback.liability IS '责任（1阿⾥责任，2⼚商责任）';
COMMENT ON COLUMN ps_delivery_feedback.abnormal_category_first IS '⼀级原因分类';
COMMENT ON COLUMN ps_delivery_feedback.abnormal_category_second IS '⼆级原因分类';
COMMENT ON COLUMN ps_delivery_feedback.abnormal_category_third IS '三级原因分类';
COMMENT ON COLUMN ps_delivery_feedback.remark IS '原因';
COMMENT ON COLUMN ps_delivery_feedback.abnormal_no IS '延期编号(不能重复)';
COMMENT ON COLUMN ps_delivery_feedback.operate_type IS '操作类型，1-人工，2-定时任务';
COMMENT ON COLUMN ps_delivery_feedback.push_status IS '推送状态：1-已推送消息未收到阿里回调通知，2-已推送消息已收到阿里回调通知';
COMMENT ON COLUMN ps_delivery_feedback.push_error_msg IS '推送报错消息';
COMMENT ON COLUMN ps_delivery_feedback.enabled_flag IS '有效标识，Y-有效，N-无效';
COMMENT ON COLUMN ps_delivery_feedback.schedule_flag IS ' 定时任务调度标识，Y-已调度，N-未调度';
COMMENT ON COLUMN ps_delivery_feedback.notes IS '备注';
COMMENT ON COLUMN ps_delivery_feedback.create_by IS '创建人';
COMMENT ON COLUMN ps_delivery_feedback.create_date IS '创建时间';
COMMENT ON COLUMN ps_delivery_feedback.last_updated_by IS '最后更新人';
COMMENT ON COLUMN ps_delivery_feedback.last_updated_date IS '最后更新时间';
-- 标模任务条码推送信息表
CREATE TABLE if not exists push_std_model_sn_data (
    id varchar(64) NOT NULL, -- 主键
    sn varchar(64) NOT NULL, -- 条码
    task_no varchar(256) NOT NULL, -- 任务号
    factory_id int4 NOT NULL, -- 工厂ID
    curr_process varchar(64) NOT NULL DEFAULT 10, -- 当前推送进程
    push_status int4 NOT NULL DEFAULT 0,
    push_date timestamp, -- 推送时间
    error_msg varchar(2000),
    push_fail_count int4 NOT NULL DEFAULT 0,
	create_by varchar(32) NOT NULL DEFAULT '',
	create_date timestamp NOT NULL DEFAULT LOCALTIMESTAMP,
	last_updated_by varchar(32) NOT NULL DEFAULT '',
	last_updated_date timestamp NOT NULL DEFAULT LOCALTIMESTAMP,
	enabled_flag varchar(1) NOT NULL DEFAULT 'Y',
	CONSTRAINT pk_push_std_model_sn_data PRIMARY KEY (id)
);
CREATE UNIQUE INDEX if not exists uni_psmsd_task_no_sn ON push_std_model_sn_data (task_no, sn, enabled_flag);
CREATE INDEX if not exists idx_psmsd_last_updated_date ON push_std_model_sn_data (last_updated_date);
CREATE INDEX if not exists idx_psmsd_sn ON push_std_model_sn_data (sn);
COMMENT ON TABLE push_std_model_sn_data IS '标模任务条码推送信息表';
-- Column comments
COMMENT ON COLUMN push_std_model_sn_data.sn IS '条码';
COMMENT ON COLUMN push_std_model_sn_data.task_no IS '任务号';
COMMENT ON COLUMN push_std_model_sn_data.factory_id IS '工厂id';
COMMENT ON COLUMN push_std_model_sn_data.curr_process IS '当前推送进程 30:产品SN上报 40:成品入库上报';
COMMENT ON COLUMN push_std_model_sn_data.push_status IS '当前进程数据推送状态 0:待推送 1:已推送未回调 2:回调成功 8:数据或推送异常 9:回调结果异常';
COMMENT ON COLUMN push_std_model_sn_data.push_date IS '当前进程数据推送时间';
COMMENT ON COLUMN push_std_model_sn_data.error_msg IS '异常信息';
COMMENT ON COLUMN push_std_model_sn_data.push_fail_count IS '当前进程推送失败次数';


COMMENT ON COLUMN push_std_model_data.push_fail_count IS '当前进程推送失败次数';

CREATE INDEX if not exists idx_pbdd_last_updated_date ON push_board_data_detail USING btree (last_updated_date);

-- 单板数据推送进程明细表
CREATE TABLE if not exists push_board_data_process (
    id varchar(64) NOT NULL, -- 主键id
    sn varchar(64) NOT NULL, -- 单板条码
    business_type varchar(64) NOT NULL, -- 业务类型AOI,FT等
    push_status int4 NOT NULL DEFAULT 0,
    push_date timestamp, -- 推送时间
    error_msg varchar(2000),
    push_fail_count int4 NOT NULL DEFAULT 0,
	create_by varchar(32) NOT NULL DEFAULT '',
	create_date timestamp NOT NULL DEFAULT LOCALTIMESTAMP,
	last_updated_by varchar(32) NOT NULL DEFAULT '',
	last_updated_date timestamp NOT NULL DEFAULT LOCALTIMESTAMP,
	enabled_flag varchar(1) NOT NULL DEFAULT 'Y',
	CONSTRAINT pk_push_board_data_process PRIMARY KEY (id)
);
CREATE INDEX if not exists idx_pbdp_sn_bustype_status ON push_board_data_process (sn, business_type, push_status, enabled_flag);
CREATE INDEX if not exists idx_pbdp_last_updated_date ON push_board_data_process (last_updated_date);
COMMENT ON TABLE push_board_data_process IS '单板数据推送进程明细表';
-- Column comments
COMMENT ON COLUMN push_board_data_process.id IS '主键id';
COMMENT ON COLUMN push_board_data_process.sn IS '条码';
COMMENT ON COLUMN push_board_data_process.business_type IS '业务类型AOI,FT等';
COMMENT ON COLUMN push_board_data_process.push_status IS 'AOI检测数据推送状态 0:待推送 1:已推送未回调 2:回调成功 8:数据或推送异常 9:回调结果异常';
COMMENT ON COLUMN push_board_data_process.push_date IS '数据推送时间';
COMMENT ON COLUMN push_board_data_process.error_msg IS '异常信息';


-- 标模任务推送信息表
CREATE TABLE if not exists push_std_model_data (
    task_no varchar(256) NOT NULL, -- 任务号
    item_no varchar(64) NOT NULL,
    task_qty int4 NOT NULL, -- 任务数量
    factory_id int4 NOT NULL, -- 工厂ID
    customer_name varchar(64), -- 客户名称
    curr_process varchar(64) NOT NULL DEFAULT 10, -- 当前推送进程
    push_status int4 NOT NULL DEFAULT 0,
    push_date timestamp, -- 推送时间
    error_msg varchar(2000),
    push_fail_count int4 NOT NULL DEFAULT 0,
	create_by varchar(32) NOT NULL DEFAULT '',
	create_date timestamp NOT NULL DEFAULT LOCALTIMESTAMP,
	last_updated_by varchar(32) NOT NULL DEFAULT '',
	last_updated_date timestamp NOT NULL DEFAULT LOCALTIMESTAMP,
	enabled_flag varchar(1) NOT NULL DEFAULT 'Y',
	CONSTRAINT pk_push_std_model_data PRIMARY KEY (task_no)
);
CREATE INDEX if not exists idx_psmd_last_updated_date ON push_std_model_data (last_updated_date);
COMMENT ON TABLE push_std_model_data IS '标模推送信息头表';
-- Column comments
COMMENT ON COLUMN push_std_model_data.task_no IS '任务号';
COMMENT ON COLUMN push_std_model_data.item_no IS '料单代码';
COMMENT ON COLUMN push_std_model_data.task_qty IS '任务数量';
COMMENT ON COLUMN push_std_model_data.factory_id IS '工厂id';
COMMENT ON COLUMN push_std_model_data.customer_name IS '客户名称';
COMMENT ON COLUMN push_std_model_data.curr_process IS '当前推送进程 10:排产信息推送 20:工单发放';
COMMENT ON COLUMN push_std_model_data.push_status IS '当前进程数据推送状态 0:待推送 1:已推送未回调 2:回调成功 8:数据或推送异常 9:回调结果异常';
COMMENT ON COLUMN push_std_model_data.push_date IS '当前进程数据推送时间';
COMMENT ON COLUMN push_std_model_data.error_msg IS '异常信息';


alter table push_board_data_detail add if not exists push_status int4 not null default 0;
COMMENT ON COLUMN push_board_data_detail.push_status IS '数据推送状态 0:待推送 1:推送完成 9:推送异常';

alter table push_board_data_head add if not exists push_status int4 not null default 0;
COMMENT ON COLUMN push_board_data_head.push_status IS '数据推送状态 0:待推送 1:推送完成 9:推送异常';
-- 删除字段
alter table push_board_data_head DROP COLUMN if exists aoi_push_status;
alter table push_board_data_detail DROP COLUMN if exists aoi_push_status;
alter table push_board_data_detail DROP COLUMN if exists aoi_push_date;
alter table push_board_data_detail DROP COLUMN if exists aoi_push_fail_time;


# getCustomerItemsByCustomerAndMode 接口文档

## 接口概述

按客户名称和合作模式查询客户物料信息

## 接口基本信息

| 项目 | 值 |
|------|---|
| 接口名称 | getCustomerItemsByCustomerAndMode |
| 请求方式 | POST |
| Content-Type | application/json |
| 接口描述 | 按客户名称和合作模式查询客户物料信息 |

## 完整URL

```
http://localhost:8080/zte-mes-manufactureshare-centerfactory/customerItemsCtrl/getCustomerItemsByCustomerAndMode
```

**URL格式说明：**
- **协议**：http/https
- **主机**：{服务器地址}
- **端口**：8080（默认）
- **上下文路径**：/zte-mes-manufactureshare-centerfactory
- **控制器路径**：/customerItemsCtrl
- **方法路径**：/getCustomerItemsByCustomerAndMode

## 请求参数

### 请求体参数 (JSON)

| 参数名 | 类型 | 必填 | 描述 | 限制 |
|--------|------|------|------|------|
| customerName | String | 是 | 客户名称 | 不能为空 |
| cooperationMode | String | 是 | 合作模式 | 不能为空 |
| itemNoList | List&lt;String&gt; | 否 | ZTE代码列表 | 最多500个 |

### 参数说明

1. **customerName**：客户名称，用于筛选特定客户的物料信息
2. **cooperationMode**：合作模式，用于筛选特定合作模式下的物料信息
3. **itemNoList**：ZTE代码列表，可选参数，用于进一步筛选特定的物料代码，如果提供则最多支持500个

## 请求示例

### 示例1：基本查询（只使用必填参数）

```http
POST /zte-mes-manufactureshare-centerfactory/customerItemsCtrl/getCustomerItemsByCustomerAndMode
Content-Type: application/json

{
    "customerName": "华为技术有限公司",
    "cooperationMode": "ODM"
}
```

### 示例2：带ZTE代码列表的查询

```http
POST /zte-mes-manufactureshare-centerfactory/customerItemsCtrl/getCustomerItemsByCustomerAndMode
Content-Type: application/json

{
    "customerName": "华为技术有限公司",
    "cooperationMode": "ODM",
    "itemNoList": [
        "123456789012",
        "123456789013",
        "123456789014"
    ]
}
```

### CURL 示例

```bash
curl -X POST "http://localhost:8080/zte-mes-manufactureshare-centerfactory/customerItemsCtrl/getCustomerItemsByCustomerAndMode" \
  -H "Content-Type: application/json" \
  -d '{
    "customerName": "华为技术有限公司",
    "cooperationMode": "ODM",
    "itemNoList": ["123456789012", "123456789013"]
  }'
```

## 响应结果

### 成功响应

**HTTP状态码：** 200 OK

**响应格式：**

```json
{
    "success": true,
    "errorCode": "0",
    "errorMessage": "操作成功",
    "data": [
        {
            "id": "1234567890123456789",
            "customerName": "华为技术有限公司",
            "projectName": "5G基站项目",
            "projectPhase": "量产阶段",
            "projectType": "0",
            "cooperationMode": "ODM",
            "zteCodeName": "主控板",
            "zteCode": "123456789012",
            "customerMaterialType": "PCB板",
            "customerCode": "HW-MAIN-001",
            "customerItemName": "主控制板",
            "zteSupplier": "中兴通讯",
            "customerSupplier": "华为供应商",
            "zteBrandStyle": "ZTE-5G-MAIN-V1.0",
            "customerSpecification": "华为5G主控板规格",
            "customerModel": "HW-5G-MAIN-001",
            "customerComponentType": "主板",
            "boardType": "0",
            "pnCode": "PN123456789",
            "additionalInfoId": "ADD123456",
            "originalManufacturerName": "原厂制造商",
            "powerSpecification": "12V/5A",
            "customerNumber": "HW20231201001",
            "projType": "5G项目",
            "status": "有效",
            "remark": "备注信息",
            "createBy": "admin",
            "createDate": "2023-12-01 10:00:00",
            "lastUpdatedBy": "admin",
            "lastUpdatedDate": "2023-12-01 10:00:00"
        }
    ]
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| success | Boolean | 请求是否成功 |
| errorCode | String | 错误码，成功时为"0" |
| errorMessage | String | 错误信息，成功时为"操作成功" |
| data | Array | 客户物料信息列表 |

### 客户物料信息字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | String | 主键ID |
| customerName | String | 客户名称 |
| projectName | String | 项目名称 |
| projectPhase | String | 项目阶段 |
| projectType | String | 项目类型（0:自研主板 1:自研背板 2:自研子卡 3:整机 4:其他物料 5:整机-主板） |
| cooperationMode | String | 合作模式 |
| zteCodeName | String | ZTE代码名称 |
| zteCode | String | ZTE代码 |
| customerMaterialType | String | 客户物料型号 |
| customerCode | String | 客户代码 |
| customerItemName | String | 客户物料名称 |
| zteSupplier | String | ZTE供应商 |
| customerSupplier | String | 客户供应商 |
| zteBrandStyle | String | ZTE规格型号 |
| customerSpecification | String | 客户规格 |
| customerModel | String | 客户型号 |
| customerComponentType | String | 客户部件类型 |
| boardType | String | 板码类型（0:单卡成品 1:单卡半成品 2:多模组成品 3:多模组半成品） |
| pnCode | String | PN码 |
| additionalInfoId | String | 附加信息ID |
| originalManufacturerName | String | 原厂制造商名称 |
| powerSpecification | String | 功率规格 |
| customerNumber | String | 客户编码 |
| projType | String | 项目类型 |
| status | String | 状态 |
| remark | String | 备注 |
| createBy | String | 创建人 |
| createDate | String | 创建时间（格式：yyyy-MM-dd HH:mm:ss） |
| lastUpdatedBy | String | 最后更新人 |
| lastUpdatedDate | String | 最后更新时间（格式：yyyy-MM-dd HH:mm:ss） |

## 异常情况

### 业务异常响应

**HTTP状态码：** 200 OK（业务异常也返回200，通过success字段区分）

#### 1. 客户名称为空

```json
{
    "success": false,
    "errorCode": "BUSINESSERROR_CODE",
    "errorMessage": "客户名称不能为空",
    "data": null
}
```

#### 2. 合作模式为空

```json
{
    "success": false,
    "errorCode": "BUSINESSERROR_CODE", 
    "errorMessage": "合作模式不能为空",
    "data": null
}
```

#### 3. ZTE代码列表超出限制

```json
{
    "success": false,
    "errorCode": "BUSINESSERROR_CODE",
    "errorMessage": "一次最多只能查询500个物料代码信息",
    "data": null
}
```

### 系统异常响应

#### 1. 参数格式错误

**HTTP状态码：** 400 Bad Request

```json
{
    "success": false,
    "errorCode": "ValidationError",
    "errorMessage": "请求参数格式错误",
    "data": null
}
```

#### 2. 服务器内部错误

**HTTP状态码：** 500 Internal Server Error

```json
{
    "success": false,
    "errorCode": "ServerError",
    "errorMessage": "服务器内部错误",
    "data": null
}
```

## 错误码说明

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| CUSTOMER_NAME_NULL | 客户名称不能为空 | 请提供有效的客户名称 |
| COOPERATION_MODE_NULL | 合作模式不能为空 | 请提供有效的合作模式 |
| MAX_ITEM_NO_IS_ONCE | ZTE代码数量超出限制 | 单次查询ZTE代码数量不能超过500个 |
| ValidationError | 参数验证失败 | 检查请求参数格式和类型 |
| ServerError | 服务器错误 | 联系系统管理员 |

## 注意事项

1. **参数验证**：客户名称和合作模式为必填参数，不能为空或空字符串
2. **数量限制**：ZTE代码列表最多支持500个，超出会返回业务异常
3. **查询性能**：建议合理使用ZTE代码列表参数，避免返回过多数据影响性能
4. **数据排序**：返回结果按最后更新时间倒序排列
5. **字符编码**：请确保请求使用UTF-8编码
6. **扩展属性**：返回的数据包含扩展属性信息，通过queryAndSetProperties方法获取

## 使用场景

1. **客户物料查询**：根据客户名称和合作模式查询物料清单
2. **精确筛选**：通过ZTE代码列表精确查询特定物料信息
3. **数据集成**：为其他系统提供客户物料数据接口
4. **报表统计**：为报表系统提供基础数据支持

## 版本信息

- **接口版本**：v1.0
- **文档版本**：v1.0
- **最后更新**：2024-01-01
- **维护团队**：MES开发团队
